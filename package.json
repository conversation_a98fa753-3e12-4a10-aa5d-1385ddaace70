{"name": "with-authjs-next", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "fmt": "prettier --write '**/*' --ignore-unknown"}, "dependencies": {"@auth/pg-adapter": "^1.4.2", "@neondatabase/serverless": "^0.9.4", "next": "14.2.26", "next-auth": "^5.0.0-beta.20", "react": "^18", "react-dom": "^18", "resend": "^4.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.7", "postcss": "^8", "prettier": "^3.3.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.8", "tailwindcss": "^3.4.1", "typescript": "^5"}}